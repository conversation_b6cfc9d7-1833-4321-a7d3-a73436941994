'use client';

import React from 'react';
import { XMarkIcon, CalendarDaysIcon, MapPinIcon, CurrencyDollarIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import { AdminEvent } from '@/types';
import { uploadService } from '@/lib/upload.service';

interface EventDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: AdminEvent | null;
}

const EventDetailModal: React.FC<EventDetailModalProps> = ({
  isOpen,
  onClose,
  event
}) => {
  if (!isOpen || !event) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    if (amount === 0) return 'Free';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case 'completed':
        return 'bg-blue-500/20 text-blue-400 border border-blue-500/30';
      case 'inactive':
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
    }
  };

  const handleDownloadProposal = () => {
    if (event.event_proposal) {
      window.open(event.event_proposal, '_blank');
    }
  };

  const handleDownloadPemenang = () => {
    if (event.event_pemenang) {
      window.open(event.event_pemenang, '_blank');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gold-500/30">
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <h2 className="text-2xl font-semibold text-white">
            Event Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gold-400 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Event Image */}
            <div>
              <div className="aspect-[4/3] bg-gray-800 rounded-lg overflow-hidden mb-4">
                {event.event_image ? (
                  <img
                    src={uploadService.getOptimizedUrl(event.event_image)}
                    alt={event.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-event.jpg';
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <CalendarDaysIcon className="h-16 w-16 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Documents Section */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                  Documents
                </h3>
                
                {event.event_proposal && (
                  <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center">
                      <DocumentArrowDownIcon className="h-5 w-5 text-gold-400 mr-3" />
                      <span className="text-white">Event Proposal</span>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleDownloadProposal}
                    >
                      Download
                    </Button>
                  </div>
                )}

                {event.event_pemenang && (
                  <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center">
                      <DocumentArrowDownIcon className="h-5 w-5 text-gold-400 mr-3" />
                      <span className="text-white">Winners Document</span>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleDownloadPemenang}
                    >
                      Download
                    </Button>
                  </div>
                )}

                {!event.event_proposal && !event.event_pemenang && (
                  <p className="text-gray-400 text-sm">No documents available</p>
                )}
              </div>
            </div>

            {/* Right Column - Event Details */}
            <div className="space-y-6">
              {/* Event Title and Status */}
              <div>
                <div className="flex items-start justify-between mb-2">
                  <h1 className="text-3xl font-bold text-white">
                    {event.name}
                  </h1>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusBadgeColor(event.status)}`}>
                    {event.status}
                  </span>
                </div>
                
                {event.description && (
                  <p className="text-gray-300 leading-relaxed">
                    {event.description}
                  </p>
                )}
              </div>

              {/* Event Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                  Event Information
                </h3>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <CalendarDaysIcon className="h-5 w-5 text-gold-400 mr-3 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Event Period</p>
                      <p className="text-gray-300 text-sm">
                        {formatDate(event.start_date)} - {formatDate(event.end_date)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <MapPinIcon className="h-5 w-5 text-gold-400 mr-3 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Location</p>
                      <p className="text-gray-300 text-sm">{event.lokasi}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-5 w-5 text-gold-400 mr-3 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Registration Fee</p>
                      <p className="text-gray-300 text-sm">{formatCurrency(event.biaya_registrasi)}</p>
                    </div>
                  </div>

                  {event.metode_pembayaran && (
                    <div className="flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 text-gold-400 mr-3 flex-shrink-0" />
                      <div>
                        <p className="text-white font-medium">Payment Method</p>
                        <p className="text-gray-300 text-sm capitalize">{event.metode_pembayaran}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Organizer Information */}
              {event.eventUser && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                    Organizer
                  </h3>
                  <div className="bg-gray-800 p-4 rounded-lg">
                    <p className="text-white font-medium">{event.eventUser.name}</p>
                    <p className="text-gray-300 text-sm">{event.eventUser.email}</p>
                  </div>
                </div>
              )}

              {/* Event Dates */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                  Timeline
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-white">{formatDate(event.created_at)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Updated:</span>
                    <span className="text-white">{formatDate(event.updated_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Close Button */}
          <div className="flex justify-end mt-8 pt-6 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventDetailModal;
